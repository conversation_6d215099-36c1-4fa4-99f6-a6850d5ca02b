# Quick Start: W&B Integration

Get up and running with Weights & Biases experiment tracking in 3 minutes!

## 🚀 Quick Setup (3 steps)

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Setup W&B Authentication
Run the interactive setup:
```bash
python scripts/setup_wandb.py
```

This will:
- Ask for your W&B API key (get it from [wandb.ai/authorize](https://wandb.ai/authorize))
- Create a `.env` file with your configuration
- Test the connection

### 3. Start Training with W&B
```bash
python -m src.train.train --epochs 10 --use-wandb
```

That's it! 🎉

## 📊 What You'll See

Your W&B dashboard will show:
- **Real-time loss curves** (training & validation)
- **Generated images** updated during training
- **Reconstructed images** (original vs generated)
- **Latent space visualization** (for 2D latent spaces)
- **Hyperparameter tracking**

## 🎯 Quick Examples

### Basic Training
```bash
python -m src.train.train --epochs 5 --use-wandb
```

### 2D Latent Space (with visualization)
```bash
# First, create a 2D config
echo '{
  "input_dim": 784,
  "hidden_dim": 256,
  "latent_dim": 2,
  "num_classes": 10,
  "lr": 0.001,
  "batch_size": 128
}' > config_2d.json

# Then train
python -m src.train.train \
    --hyperparam-config-path config_2d.json \
    --epochs 10 \
    --use-wandb \
    --wandb-run-name "2d-latent-experiment"
```

### Demo with All Features
```bash
python examples/wandb_demo.py
```

## 🔧 Manual Setup (Alternative)

If you prefer manual setup:

1. **Create `.env` file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` with your API key:**
   ```env
   WANDB_API_KEY=your_api_key_here
   WANDB_PROJECT=cvae-demo
   ```

3. **Test:**
   ```bash
   python scripts/test_wandb.py
   ```

## 🎛️ Configuration Options

### Command Line
```bash
python -m src.train.train \
    --epochs 20 \
    --use-wandb \
    --wandb-project "my-experiments" \
    --wandb-run-name "baseline-v1" \
    --wandb-tags "baseline" "fashion-mnist"
```

### Environment Variables (.env)
```env
WANDB_API_KEY=your_key_here
WANDB_PROJECT=my-project
WANDB_ENTITY=my-team
WANDB_MODE=online
```

## 🔍 Troubleshooting

### Common Issues

**"WANDB_API_KEY not found"**
```bash
# Run the setup script
python scripts/setup_wandb.py
```

**"python-dotenv not installed"**
```bash
pip install python-dotenv
```

**Authentication failed**
- Check your API key at [wandb.ai/authorize](https://wandb.ai/authorize)
- Make sure `.env` file is in the project root

### Test Your Setup
```bash
python scripts/test_wandb.py
```

## 📈 Advanced Usage

### Batch Experiments
```bash
python examples/run_experiments.py
```

### Offline Mode
```env
WANDB_MODE=offline
```

### Custom Logging
```python
from src.utils.wandb_utils import init_wandb, log_losses

run = init_wandb({"lr": 0.001})
log_losses(epoch=1, train_loss=0.5, train_bce=0.3, train_kld=0.2)
```

## 📚 Full Documentation

- **Setup Guide**: `WANDB_SETUP.md`
- **Feature Guide**: `WANDB_GUIDE.md`
- **W&B Docs**: [docs.wandb.ai](https://docs.wandb.ai)

## 🎉 You're Ready!

Your W&B dashboard: [wandb.ai](https://wandb.ai)

Happy experimenting! 🚀
