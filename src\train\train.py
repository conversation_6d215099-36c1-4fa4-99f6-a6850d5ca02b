import argparse
import json
import torch
from src.utils.utils import one_hot_encode
from src.models.cvae import CVAE, loss_function
from src.utils.utils import Logger
from src.data.dataset import FashionMNISTDataset
from src.data.dataloader import DataLoader
from src.evaluate.evaluate import evaluate

def train(model, device, train_loader, optimizer, epoch, logger):
    """
    Train the model
    Args:
        model: model to train
        device: device to use
        train_loader: train loader
        optimizer: optimizer
        epoch: current epoch
        logger: logger to log the losses
    """
    model.train()
    train_loss = 0
    bce_loss_sum = 0
    kld_loss_sum = 0
    num_batches = 0

    for batch_idx, (data, labels) in enumerate(train_loader):
        data = data.to(device)
        labels = labels.to(device)

        # one hot encoding the labels
        labels_one_hot = one_hot_encode(labels, num_classes=10)

        optimizer.zero_grad()

        recon_batch, mu, logvar = model(data, labels_one_hot)
        loss, bce, kl = loss_function(recon_batch, data, mu, logvar)

        loss.backward()
        train_loss += loss.item()
        bce_loss_sum += bce.item()
        kld_loss_sum += kl.item()
        num_batches += 1
        optimizer.step()

        if batch_idx % 100 == 0:
            print(
                f"Train Epoch: {epoch} [{batch_idx * len(data)}/{len(train_loader.dataset)} "
                f"({100.0 * batch_idx / len(train_loader):.0f}%)]\t"
                f"Loss: {loss.item() / len(data):.6f} "
                f"(BCE: {bce.item() / len(data):.6f}, KL: {kl.item() / len(data):.6f})"
            )

    avg_loss = train_loss / len(train_loader.dataset)
    avg_bce = bce_loss_sum / len(train_loader.dataset)
    avg_kld = kld_loss_sum / len(train_loader.dataset)
    logger.log(epoch, avg_loss, avg_bce, avg_kld)

    print(
        f"====> Epoch: {epoch} Average loss: {avg_loss:.4f} "
        f"(BCE: {avg_bce:.4f}, KL: {avg_kld:.4f})"
    )


def load_hyperparam_config(path="hyperparam_config.json"):
    with open(path, "r") as f:
        return json.load(f)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--hyperparam-config-path", type=str, default="hyperparam_config.json", help="Path to hyperparameter configuration")
    parser.add_argument("--epochs", type=int, default=3, help="Number of epochs")
    parser.add_argument("--batch-size", type=int, default=64, help="Batch size")
    parser.add_argument("--eval-interval", type=int, default=1, help="Interval to evaluate the model")
    parser.add_argument("--device", default="cuda" if torch.cuda.is_available() else "cpu", help="Device to use")
    args = parser.parse_args()

    hyperparam_config = load_hyperparam_config(args.hyperparam_config)

    model = CVAE(
        input_dim=hyperparam_config.get("input_dim", 784),
        hidden_dim=hyperparam_config.get("hidden_dim", 400),
        latent_dim=hyperparam_config.get("latent_dim", 20),
        num_classes=hyperparam_config.get("num_classes", 10),
    )
    optimizer = torch.optim.Adam(model.parameters(), lr=hyperparam_config.get("lr", 1e-3))
    logger = Logger()
    train_dataset = FashionMNISTDataset(root="./data", train=True)
    test_dataset = FashionMNISTDataset(root="./data", train=False)
    train_loader = DataLoader(train_dataset, batch_size=hyperparam_config.get("batch_size", 64), shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=hyperparam_config.get("batch_size", 64), shuffle=False)
    for epoch in range(1, args.epochs + 1):
        train(model, args.device, train_loader, optimizer, epoch, logger)
        if epoch % args.eval_interval == 0:
            evaluate(model, args.device, test_loader)

    

if __name__ == "__main__":
    main()
