import argparse
import json
import torch
import os
from src.utils.utils import one_hot_encode
from src.models.cvae import CVAE, loss_function
from src.utils.utils import Logger
from src.data.dataset import FashionMNISTDataset
from src.data.dataloader import DataLoader
from src.evaluate.evaluate import evaluate
from src.utils.wandb_utils import (
    init_wandb, log_losses, log_generated_samples,
    log_reconstructions, log_latent_space_visualization, finish_wandb
)

def train(model, device, train_loader, optimizer, epoch, logger, use_wandb=True):
    """
    Train the model
    Args:
        model: model to train
        device: device to use
        train_loader: train loader
        optimizer: optimizer
        epoch: current epoch
        logger: logger to log the losses
        use_wandb: whether to use wandb logging
    """
    model.train()
    train_loss = 0
    bce_loss_sum = 0
    kld_loss_sum = 0
    num_batches = 0

    for batch_idx, (data, labels) in enumerate(train_loader):
        data = data.to(device)
        labels = labels.to(device)

        # one hot encoding the labels
        labels_one_hot = one_hot_encode(labels, num_classes=10)

        optimizer.zero_grad()

        recon_batch, mu, logvar = model(data, labels_one_hot)
        loss, bce, kl = loss_function(recon_batch, data, mu, logvar)

        loss.backward()
        train_loss += loss.item()
        bce_loss_sum += bce.item()
        kld_loss_sum += kl.item()
        num_batches += 1
        optimizer.step()

        if batch_idx % 100 == 0:
            print(
                f"Train Epoch: {epoch} [{batch_idx * len(data)}/{len(train_loader.dataset)} "
                f"({100.0 * batch_idx / len(train_loader):.0f}%)]\t"
                f"Loss: {loss.item() / len(data):.6f} "
                f"(BCE: {bce.item() / len(data):.6f}, KL: {kl.item() / len(data):.6f})"
            )

    avg_loss = train_loss / len(train_loader.dataset)
    avg_bce = bce_loss_sum / len(train_loader.dataset)
    avg_kld = kld_loss_sum / len(train_loader.dataset)
    logger.log(epoch, avg_loss, avg_bce, avg_kld)

    print(
        f"====> Epoch: {epoch} Average loss: {avg_loss:.4f} "
        f"(BCE: {avg_bce:.4f}, KL: {avg_kld:.4f})"
    )

    return avg_loss, avg_bce, avg_kld


def load_hyperparam_config(path="hyperparam_config.json"):
    with open(path, "r") as f:
        return json.load(f)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--hyperparam-config-path", type=str, default="hyperparam_config.json", help="Path to hyperparameter configuration")
    parser.add_argument("--epochs", type=int, default=3, help="Number of epochs")
    parser.add_argument("--batch-size", type=int, default=64, help="Batch size")
    parser.add_argument("--eval-interval", type=int, default=1, help="Interval to evaluate the model")
    parser.add_argument("--device", default="cuda" if torch.cuda.is_available() else "cpu", help="Device to use")
    parser.add_argument("--use-wandb", action="store_true", default=True, help="Use Weights & Biases for logging")
    parser.add_argument("--wandb-project", type=str, default="cvae-demo", help="W&B project name")
    parser.add_argument("--wandb-run-name", type=str, default=None, help="W&B run name")
    parser.add_argument("--wandb-tags", nargs="+", default=None, help="W&B tags for the run")
    args = parser.parse_args()

    hyperparam_config = load_hyperparam_config(args.hyperparam_config)

    # Add command line arguments to config
    config = hyperparam_config.copy()
    config.update({
        "epochs": args.epochs,
        "batch_size": args.batch_size,
        "eval_interval": args.eval_interval,
        "device": args.device
    })

    # Initialize W&B if requested
    if args.use_wandb:
        init_wandb(
            config=config,
            project_name=args.wandb_project,
            run_name=args.wandb_run_name,
            tags=args.wandb_tags
        )

    model = CVAE(
        input_dim=hyperparam_config.get("input_dim", 784),
        hidden_dim=hyperparam_config.get("hidden_dim", 400),
        latent_dim=hyperparam_config.get("latent_dim", 20),
        num_classes=hyperparam_config.get("num_classes", 10),
    ).to(args.device)

    optimizer = torch.optim.Adam(model.parameters(), lr=hyperparam_config.get("lr", 1e-3))
    logger = Logger()
    train_dataset = FashionMNISTDataset(root="./data", train=True)
    test_dataset = FashionMNISTDataset(root="./data", train=False)
    train_loader = DataLoader(train_dataset, batch_size=hyperparam_config.get("batch_size", 64), shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=hyperparam_config.get("batch_size", 64), shuffle=False)

    print(f"Starting training for {args.epochs} epochs on {args.device}")
    print(f"Model: CVAE with {hyperparam_config.get('hidden_dim', 400)} hidden dim, {hyperparam_config.get('latent_dim', 20)} latent dim")

    for epoch in range(1, args.epochs + 1):
        # Training
        train_loss, train_bce, train_kld = train(model, args.device, train_loader, optimizer, epoch, logger)

        # Evaluation
        test_loss, test_bce, test_kld = None, None, None
        if epoch % args.eval_interval == 0:
            test_loss, test_bce, test_kld = evaluate(model, args.device, test_loader)

        # W&B logging
        if args.use_wandb:
            log_losses(epoch, train_loss, train_bce, train_kld, test_loss, test_bce, test_kld)

            # Log generated samples every few epochs
            if epoch % max(1, args.epochs // 5) == 0 or epoch == args.epochs:
                log_generated_samples(model, args.device, epoch)
                log_reconstructions(model, args.device, test_loader, epoch)

                # Log latent space visualization if 2D
                if hyperparam_config.get("latent_dim", 20) == 2:
                    log_latent_space_visualization(model, args.device, test_loader, epoch)

    # Finish W&B logging
    if args.use_wandb:
        finish_wandb()

    print("Training completed!")

    

if __name__ == "__main__":
    main()
