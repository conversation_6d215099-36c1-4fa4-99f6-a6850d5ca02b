#!/usr/bin/env python3
"""
Example script showing how to run multiple experiments with different configurations
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import json
import subprocess
import time

def create_config(config_name, **kwargs):
    """Create a hyperparameter configuration file"""
    base_config = {
        "input_dim": 784,
        "hidden_dim": 400,
        "latent_dim": 20,
        "num_classes": 10,
        "lr": 0.001,
        "batch_size": 64,
        "beta": 1.0,
        "optimizer": "Adam"
    }
    
    # Update with provided kwargs
    base_config.update(kwargs)
    
    config_path = f"config_{config_name}.json"
    with open(config_path, 'w') as f:
        json.dump(base_config, f, indent=2)
    
    return config_path

def run_experiment(config_path, run_name, tags, epochs=10):
    """Run a single experiment"""
    cmd = [
        "python", "-m", "src.train.train",
        "--hyperparam-config-path", config_path,
        "--epochs", str(epochs),
        "--use-wandb",
        "--wandb-run-name", run_name,
        "--wandb-tags"
    ] + tags
    
    print(f"Running experiment: {run_name}")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✓ Experiment {run_name} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Experiment {run_name} failed:")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Run multiple experiments with different configurations"""
    
    experiments = [
        {
            "name": "baseline",
            "config": {"hidden_dim": 400, "latent_dim": 20, "lr": 0.001},
            "tags": ["baseline", "standard"]
        },
        {
            "name": "large_hidden",
            "config": {"hidden_dim": 800, "latent_dim": 20, "lr": 0.001},
            "tags": ["large-hidden", "architecture"]
        },
        {
            "name": "small_latent",
            "config": {"hidden_dim": 400, "latent_dim": 10, "lr": 0.001},
            "tags": ["small-latent", "architecture"]
        },
        {
            "name": "2d_latent",
            "config": {"hidden_dim": 256, "latent_dim": 2, "lr": 0.001},
            "tags": ["2d-latent", "visualization"]
        },
        {
            "name": "high_lr",
            "config": {"hidden_dim": 400, "latent_dim": 20, "lr": 0.01},
            "tags": ["high-lr", "learning-rate"]
        },
        {
            "name": "low_lr",
            "config": {"hidden_dim": 400, "latent_dim": 20, "lr": 0.0001},
            "tags": ["low-lr", "learning-rate"]
        }
    ]
    
    print("Starting batch experiments...")
    print(f"Total experiments: {len(experiments)}")
    
    successful = 0
    failed = 0
    
    for i, exp in enumerate(experiments, 1):
        print(f"\n{'='*50}")
        print(f"Experiment {i}/{len(experiments)}: {exp['name']}")
        print(f"{'='*50}")
        
        # Create configuration file
        config_path = create_config(exp['name'], **exp['config'])
        
        # Run experiment
        success = run_experiment(
            config_path=config_path,
            run_name=f"exp_{exp['name']}",
            tags=exp['tags'],
            epochs=5  # Short runs for demo
        )
        
        if success:
            successful += 1
        else:
            failed += 1
        
        # Clean up config file
        try:
            os.remove(config_path)
        except:
            pass
        
        # Small delay between experiments
        if i < len(experiments):
            print("Waiting 10 seconds before next experiment...")
            time.sleep(10)
    
    print(f"\n{'='*50}")
    print("BATCH EXPERIMENTS COMPLETED")
    print(f"{'='*50}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Total: {len(experiments)}")
    
    if successful > 0:
        print("\n🎉 Check your W&B dashboard to compare results!")
        print("Visit: https://wandb.ai")

if __name__ == "__main__":
    main()
