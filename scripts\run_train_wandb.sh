#!/bin/bash

# Training script with W&B integration
# Usage: ./scripts/run_train_wandb.sh [epochs] [run_name]

EPOCHS=${1:-10}
RUN_NAME=${2:-"cvae-training-$(date +%Y%m%d-%H%M%S)"}

echo "Starting CVAE training with W&B logging..."
echo "Epochs: $EPOCHS"
echo "Run name: $RUN_NAME"
echo "Project: cvae-demo"

python -m src.train.train \
    --epochs $EPOCHS \
    --use-wandb \
    --wandb-project "cvae-demo" \
    --wandb-run-name "$RUN_NAME" \
    --wandb-tags "training" "fashion-mnist"

echo "Training completed! Check your W&B dashboard for results."
