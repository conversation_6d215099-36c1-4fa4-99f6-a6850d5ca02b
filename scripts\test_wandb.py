#!/usr/bin/env python3
"""
Quick test script for W&B integration
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import torch
from src.utils.wandb_utils import init_wandb, log_losses, finish_wandb

def test_wandb():
    """Test basic W&B functionality"""
    print("Testing W&B integration...")
    
    # Test configuration
    config = {
        "test": True,
        "model": "CVAE",
        "dataset": "FashionMNIST"
    }
    
    try:
        # Initialize W&B
        run = init_wandb(
            config=config,
            project_name="cvae-demo-test",
            run_name="integration-test",
            tags=["test"]
        )
        
        print("✓ W&B initialization successful")
        
        # Test logging
        for epoch in range(1, 4):
            train_loss = 1.0 / epoch  # Decreasing loss
            train_bce = 0.6 / epoch
            train_kld = 0.4 / epoch
            
            log_losses(epoch, train_loss, train_bce, train_kld)
            print(f"✓ Logged losses for epoch {epoch}")
        
        print(f"✓ W&B test completed successfully!")
        print(f"Check your results at: {run.url}")
        
        # Finish W&B
        finish_wandb()
        
    except Exception as e:
        print(f"✗ W&B test failed: {e}")
        print("Make sure you have run 'wandb login' first")
        return False
    
    return True

if __name__ == "__main__":
    success = test_wandb()
    if success:
        print("\n🎉 W&B integration is working correctly!")
        print("You can now run the full training with: python -m src.train.train --use-wandb")
    else:
        print("\n❌ W&B integration test failed. Please check your setup.")
