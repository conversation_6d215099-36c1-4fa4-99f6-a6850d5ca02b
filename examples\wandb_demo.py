#!/usr/bin/env python3
"""
Demonstration of W&B integration with CVAE
This script shows how to use all the W&B logging features
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import torch
import json
from src.models.cvae import CVAE, loss_function
from src.data.dataset import FashionMNISTDataset
from src.data.dataloader import DataLoader
from src.utils.utils import one_hot_encode
from src.utils.wandb_utils import (
    init_wandb, log_losses, log_generated_samples, 
    log_reconstructions, log_latent_space_visualization, finish_wandb
)


def demo_wandb_integration():
    """
    Demonstrate W&B integration with a short training run
    """
    # Configuration
    config = {
        "input_dim": 784,
        "hidden_dim": 256,
        "latent_dim": 2,  # Use 2D for latent space visualization
        "num_classes": 10,
        "lr": 0.001,
        "batch_size": 128,
        "epochs": 5,
        "device": "cuda" if torch.cuda.is_available() else "cpu"
    }
    
    print("Starting W&B Demo...")
    print(f"Configuration: {json.dumps(config, indent=2)}")
    
    # Initialize W&B
    run = init_wandb(
        config=config,
        project_name="cvae-demo",
        run_name="wandb-demo-run",
        tags=["demo", "2d-latent", "short-training"]
    )
    
    # Setup model and data
    device = config["device"]
    model = CVAE(
        input_dim=config["input_dim"],
        hidden_dim=config["hidden_dim"],
        latent_dim=config["latent_dim"],
        num_classes=config["num_classes"]
    ).to(device)
    
    optimizer = torch.optim.Adam(model.parameters(), lr=config["lr"])
    
    # Load data
    train_dataset = FashionMNISTDataset(root="./data", train=True)
    test_dataset = FashionMNISTDataset(root="./data", train=False)
    train_loader = DataLoader(train_dataset, batch_size=config["batch_size"], shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=config["batch_size"], shuffle=False)
    
    print(f"Training on {device} for {config['epochs']} epochs")
    
    # Training loop with W&B logging
    for epoch in range(1, config["epochs"] + 1):
        # Training
        model.train()
        train_loss = 0
        train_bce = 0
        train_kld = 0
        
        for batch_idx, (data, labels) in enumerate(train_loader):
            data = data.to(device)
            labels = labels.to(device)
            labels_one_hot = one_hot_encode(labels, num_classes=10)
            
            optimizer.zero_grad()
            recon_batch, mu, logvar = model(data, labels_one_hot)
            loss, bce, kl = loss_function(recon_batch, data, mu, logvar)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_bce += bce.item()
            train_kld += kl.item()
            
            if batch_idx % 100 == 0:
                print(f"Epoch {epoch}, Batch {batch_idx}: Loss = {loss.item():.4f}")
        
        # Calculate averages
        avg_train_loss = train_loss / len(train_loader.dataset)
        avg_train_bce = train_bce / len(train_loader.dataset)
        avg_train_kld = train_kld / len(train_loader.dataset)
        
        # Evaluation
        model.eval()
        test_loss = 0
        test_bce = 0
        test_kld = 0
        
        with torch.no_grad():
            for data, labels in test_loader:
                data = data.to(device)
                labels = labels.to(device)
                labels_one_hot = one_hot_encode(labels, num_classes=10)
                
                recon_batch, mu, logvar = model(data, labels_one_hot)
                loss, bce, kld = loss_function(recon_batch, data, mu, logvar)
                
                test_loss += loss.item()
                test_bce += bce.item()
                test_kld += kld.item()
        
        avg_test_loss = test_loss / len(test_loader.dataset)
        avg_test_bce = test_bce / len(test_loader.dataset)
        avg_test_kld = test_kld / len(test_loader.dataset)
        
        print(f"Epoch {epoch}: Train Loss = {avg_train_loss:.4f}, Test Loss = {avg_test_loss:.4f}")
        
        # Log losses to W&B
        log_losses(epoch, avg_train_loss, avg_train_bce, avg_train_kld,
                  avg_test_loss, avg_test_bce, avg_test_kld)
        
        # Log images every epoch (since it's a short demo)
        log_generated_samples(model, device, epoch, num_samples_per_class=2)
        log_reconstructions(model, device, test_loader, epoch, num_samples=8)
        
        # Log latent space visualization (works because latent_dim=2)
        log_latent_space_visualization(model, device, test_loader, epoch, num_samples=500)
    
    print("Demo completed! Check your W&B dashboard for results.")
    print(f"W&B run URL: {run.url}")
    
    # Finish W&B logging
    finish_wandb()


if __name__ == "__main__":
    demo_wandb_integration()
