# W&B Setup Guide with API Key Authentication

This guide shows you how to set up Weights & Biases (W&B) authentication using an API key stored in a `.env` file for automatic login.

## Quick Setup

### 1. Install Dependencies

```bash
pip install python-dotenv wandb
```

Or install all requirements:
```bash
pip install -r requirements.txt
```

### 2. Get Your W&B API Key

1. Go to [https://wandb.ai/authorize](https://wandb.ai/authorize)
2. Sign in to your W&B account (create one if needed)
3. Copy your API key

### 3. Create .env File

Create a `.env` file in your project root directory:

```bash
cp .env.example .env
```

Edit the `.env` file and add your API key:

```env
# Weights & Biases Configuration
WANDB_API_KEY=your_actual_api_key_here

# Optional: Set default project name
WANDB_PROJECT=cvae-demo

# Optional: Set your W&B username or team name
# WANDB_ENTITY=your_username

# Optional: Set W&B mode (online, offline, disabled)
# WANDB_MODE=online
```

### 4. Test the Setup

Run the test script to verify everything works:

```bash
python scripts/test_wandb.py
```

You should see:
```
Testing W&B integration...
✓ W&B authentication successful
✓ W&B initialization successful
  Project: cvae-demo-test
  Run: integration-test
  URL: https://wandb.ai/...
✓ Logged losses for epoch 1
✓ Logged losses for epoch 2
✓ Logged losses for epoch 3
✓ W&B test completed successfully!

🎉 W&B integration is working correctly!
```

## Environment Variables Reference

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `WANDB_API_KEY` | Your W&B API key | None | **Yes** |
| `WANDB_PROJECT` | Default project name | `cvae-demo` | No |
| `WANDB_ENTITY` | Your username or team | None | No |
| `WANDB_MODE` | Logging mode | `online` | No |
| `WANDB_SILENT` | Disable prompts | `false` | No |

### WANDB_MODE Options

- `online`: Log to W&B servers (default)
- `offline`: Save logs locally, sync later
- `disabled`: Disable W&B completely

## Usage Examples

### Basic Training with Auto-Login

```bash
python -m src.train.train --epochs 10 --use-wandb
```

### Custom Project and Run Name

```bash
python -m src.train.train \
    --epochs 20 \
    --use-wandb \
    --wandb-project "my-experiments" \
    --wandb-run-name "baseline-run"
```

### Using Environment Variables

Set in `.env`:
```env
WANDB_PROJECT=my-custom-project
WANDB_ENTITY=my-team
```

Then run:
```bash
python -m src.train.train --epochs 10 --use-wandb
```

## Offline Mode

To work offline and sync later:

```env
WANDB_MODE=offline
```

Sync later with:
```bash
wandb sync wandb/offline-run-*
```

## Troubleshooting

### Common Issues

1. **"WANDB_API_KEY not found"**
   - Make sure `.env` file exists in project root
   - Check that the API key is correctly set
   - Verify no extra spaces around the key

2. **"python-dotenv not installed"**
   ```bash
   pip install python-dotenv
   ```

3. **"W&B authentication failed"**
   - Verify your API key is valid
   - Check internet connection
   - Try regenerating your API key

4. **Permission errors**
   - Make sure `.env` file is readable
   - Check file permissions

### Debug Steps

1. **Check if .env is loaded:**
   ```python
   import os
   print(f"API Key loaded: {'Yes' if os.getenv('WANDB_API_KEY') else 'No'}")
   ```

2. **Test manual login:**
   ```python
   import wandb
   wandb.login(key="your_api_key_here")
   ```

3. **Check W&B status:**
   ```bash
   wandb status
   ```

## Security Notes

- **Never commit `.env` to version control**
- The `.env` file is already in `.gitignore`
- Use different API keys for different environments
- Regenerate keys if compromised

## Advanced Configuration

### Team/Organization Setup

```env
WANDB_ENTITY=my-organization
WANDB_PROJECT=shared-project
```

### Custom Base URL (for W&B Server)

```env
WANDB_BASE_URL=https://your-wandb-server.com
```

### Disable Specific Features

```env
WANDB_SILENT=true
WANDB_CONSOLE=off
```

## Integration in Code

The automatic authentication is handled in `src/utils/wandb_utils.py`:

```python
from src.utils.wandb_utils import init_wandb

# This will automatically use the API key from .env
run = init_wandb(config={"lr": 0.001})
```

## Next Steps

Once setup is complete:

1. Run the demo: `python examples/wandb_demo.py`
2. Start training: `python -m src.train.train --use-wandb`
3. Check your dashboard: [https://wandb.ai](https://wandb.ai)

## Support

- W&B Documentation: [https://docs.wandb.ai](https://docs.wandb.ai)
- W&B Community: [https://community.wandb.ai](https://community.wandb.ai)
- API Key Issues: [https://wandb.ai/authorize](https://wandb.ai/authorize)
