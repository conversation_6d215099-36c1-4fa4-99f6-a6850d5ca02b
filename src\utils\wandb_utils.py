"""
Weights & Biases utilities for CVAE project
"""
import wandb
import torch
import matplotlib.pyplot as plt
import numpy as np
import os
from src.evaluate.evaluate import generate_images
from typing import Dict, Any, Optional, List


from src.config import WANDB_PROJECT

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not installed. Install with: pip install python-dotenv")


def setup_wandb_auth():
    """
    Setup W&B authentication using API key from environment variables
    """
    api_key = os.getenv('WANDB_API_KEY')

    if api_key:
        # Set the API key in the environment for wandb to use
        os.environ['WANDB_API_KEY'] = api_key

        # Try to login programmatically
        try:
            wandb.login(key=api_key)
            print("✓ W&B authentication successful")
            return True
        except Exception as e:
            print(f"Warning: W&B login failed: {e}")
            print("Please check your WANDB_API_KEY in the .env file")
            return False
    else:
        print("Warning: WANDB_API_KEY not found in environment variables")
        print("Please create a .env file with your W&B API key")
        print("Get your API key from: https://wandb.ai/authorize")
        return False


def init_wandb(config: Dict[str, Any], project_name: str = None,
               run_name: Optional[str] = None, tags: Optional[List[str]] = None):
    """
    Initialize Weights & Biases for experiment tracking

    Args:
        config: Dictionary containing hyperparameters and configuration
        project_name: Name of the W&B project (defaults to env var or config)
        run_name: Optional name for this specific run
        tags: Optional list of tags for organizing runs

    Returns:
        wandb run object or None if authentication fails
    """
    # Setup authentication first
    if not setup_wandb_auth():
        print("W&B authentication failed. Continuing without W&B logging.")
        return None

    # Get project name from environment variable if not provided
    if project_name is None:
        project_name = os.getenv('WANDB_PROJECT', WANDB_PROJECT)

    # Get entity from environment variable if set
    entity = os.getenv('WANDB_ENTITY', None)

    # Get mode from environment variable (online, offline, disabled)
    mode = os.getenv('WANDB_MODE', 'online')

    try:
        run = wandb.init(
            project=project_name,
            entity=entity,
            name=run_name,
            config=config,
            tags=tags or [],
            mode=mode,
            reinit=True
        )

        # Log model architecture info
        wandb.config.update({
            "model_type": "CVAE",
            "dataset": "FashionMNIST",
            "input_dim": config.get("input_dim", 784),
            "hidden_dim": config.get("hidden_dim", 400),
            "latent_dim": config.get("latent_dim", 20),
            "num_classes": config.get("num_classes", 10)
        })

        print("✓ W&B initialized successfully")
        print(f"  Project: {project_name}")
        print(f"  Run: {run.name}")
        print(f"  URL: {run.url}")

        return run

    except Exception as e:
        print(f"Failed to initialize W&B: {e}")
        return None


def log_losses(epoch: int, train_loss: float, train_bce: float, train_kld: float,
               test_loss: Optional[float] = None, test_bce: Optional[float] = None,
               test_kld: Optional[float] = None):
    """
    Log training and validation losses to W&B

    Args:
        epoch: Current epoch number
        train_loss: Training total loss
        train_bce: Training BCE loss
        train_kld: Training KLD loss
        test_loss: Optional test total loss
        test_bce: Optional test BCE loss
        test_kld: Optional test KLD loss
    """
    log_dict = {
        "epoch": epoch,
        "train/total_loss": train_loss,
        "train/bce_loss": train_bce,
        "train/kld_loss": train_kld,
    }

    if test_loss is not None:
        log_dict.update({
            "test/total_loss": test_loss,
            "test/bce_loss": test_bce,
            "test/kld_loss": test_kld,
        })

    wandb.log(log_dict)


def log_images(images: torch.Tensor, labels: torch.Tensor, epoch: int,
               prefix: str = "generated", max_images: int = 16):
    """
    Log images to W&B

    Args:
        images: Tensor of images to log (batch_size, channels, height, width)
        labels: Tensor of corresponding labels
        epoch: Current epoch number
        prefix: Prefix for the logged images (e.g., 'generated', 'reconstructed')
        max_images: Maximum number of images to log
    """
    # Limit number of images
    if images.size(0) > max_images:
        images = images[:max_images]
        labels = labels[:max_images]

    # Convert to numpy and ensure proper format
    if images.dim() == 4:  # (batch, channels, height, width)
        images_np = images.detach().cpu().numpy()
    else:  # (batch, height, width) - add channel dimension
        images_np = images.detach().cpu().unsqueeze(1).numpy()

    labels_np = labels.detach().cpu().numpy() if torch.is_tensor(labels) else labels

    # Fashion-MNIST class names
    class_names = [
        "T-shirt/top", "Trouser", "Pullover", "Dress", "Coat",
        "Sandal", "Shirt", "Sneaker", "Bag", "Ankle boot"
    ]

    # Create wandb images with labels
    wandb_images = []
    for i in range(len(images_np)):
        img = images_np[i]
        if img.shape[0] == 1:  # Remove channel dimension for grayscale
            img = img.squeeze(0)

        label_idx = int(labels_np[i]) if hasattr(labels_np[i], '__int__') else labels_np[i]
        caption = f"{class_names[label_idx]} (class {label_idx})"

        wandb_images.append(wandb.Image(img, caption=caption))

    wandb.log({f"{prefix}_images": wandb_images, "epoch": epoch})


def log_generated_samples(model, device: str, epoch: int, num_samples_per_class: int = 2):
    """
    Generate and log sample images for each class

    Args:
        model: The CVAE model
        device: Device to run inference on
        epoch: Current epoch number
        num_samples_per_class: Number of samples to generate per class
    """
    model.eval()
    classes = [
        "T-shirt/top",
        "Trouser",
        "Pullover",
        "Dress",
        "Coat",
        "Sandal",
        "Shirt",
        "Sneaker",
        "Bag",
        "Ankle boot",
    ]
    with torch.no_grad():
        for class_idx in range(10):  # 10 classes in Fashion-MNIST
            samples = generate_images(model, device, class_idx)
            for j in range(5):
                plt.subplot(1, 5, j + 1)
                plt.imshow(samples[j].cpu().numpy(), cmap="gray")
                plt.title(classes[class_idx])
                plt.axis("off")
            wandb.log({
                "generated_images": wandb.Image(plt),
                "epoch": epoch
            })
            plt.close() 


def log_reconstructions(model, device: str, data_loader, epoch: int, num_samples: int = 8):
    """
    Log original vs reconstructed images

    Args:
        model: The CVAE model
        device: Device to run inference on
        data_loader: DataLoader to get samples from
        epoch: Current epoch number
        num_samples: Number of reconstruction pairs to log
    """
    model.eval()

    # Get a batch of data
    data_iter = iter(data_loader)
    data, labels = next(data_iter)

    # Take only the requested number of samples
    data = data[:num_samples].to(device)
    labels = labels[:num_samples].to(device)

    # One-hot encode labels
    from src.utils.utils import one_hot_encode
    labels_one_hot = one_hot_encode(labels, num_classes=10)

    with torch.no_grad():
        # Get reconstructions
        recon_data, _, _ = model(data, labels_one_hot)

        # Reshape for visualization
        original = data.view(-1, 28, 28)
        reconstructed = recon_data.view(-1, 28, 28)

        # Create comparison images (original on top, reconstruction below)
        comparison = torch.cat([original, reconstructed], dim=0)
        comparison_labels = torch.cat([labels, labels], dim=0)

        # Log to W&B
        log_images(comparison, comparison_labels, epoch, prefix="reconstructions")


def log_latent_space_visualization(model, device: str, data_loader, epoch: int, num_samples: int = 1000):
    """
    Create and log a 2D visualization of the latent space (only works for 2D latent space)

    Args:
        model: The CVAE model
        device: Device to run inference on
        data_loader: DataLoader to get samples from
        epoch: Current epoch number
        num_samples: Number of samples to use for visualization
    """
    if model.latent_dim != 2:
        print(f"Latent space visualization only supported for 2D latent space, got {model.latent_dim}D")
        return

    model.eval()

    latent_codes = []
    labels_list = []

    with torch.no_grad():
        sample_count = 0
        for data, labels in data_loader:
            if sample_count >= num_samples:
                break

            data = data.to(device)
            labels = labels.to(device)

            # One-hot encode labels
            from src.utils.utils import one_hot_encode
            labels_one_hot = one_hot_encode(labels, num_classes=10)

            # Encode to latent space
            mu, _ = model.encode(data, labels_one_hot)

            latent_codes.append(mu.cpu())
            labels_list.append(labels.cpu())

            sample_count += len(data)

    # Concatenate all latent codes and labels
    latent_codes = torch.cat(latent_codes, dim=0)[:num_samples]
    labels_list = torch.cat(labels_list, dim=0)[:num_samples]

    # Create scatter plot
    plt.figure(figsize=(10, 8))
    colors = plt.cm.tab10(np.linspace(0, 1, 10))
    class_names = [
        "T-shirt/top", "Trouser", "Pullover", "Dress", "Coat",
        "Sandal", "Shirt", "Sneaker", "Bag", "Ankle boot"
    ]

    for i in range(10):
        mask = labels_list == i
        if mask.sum() > 0:
            plt.scatter(latent_codes[mask, 0], latent_codes[mask, 1],
                       c=[colors[i]], label=class_names[i], alpha=0.6, s=20)

    plt.xlabel('Latent Dimension 1')
    plt.ylabel('Latent Dimension 2')
    plt.title(f'Latent Space Visualization - Epoch {epoch}')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()

    # Log to W&B
    wandb.log({"latent_space": wandb.Image(plt), "epoch": epoch})
    plt.close()


def finish_wandb():
    """
    Finish the W&B run
    """
    wandb.finish()