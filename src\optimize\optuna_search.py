import optuna
import torch
import torch.optim as optim
import json
import argparse
from sklearn.model_selection import train_test_split
from src.models.cvae import CVAE, loss_function
from src.utils.utils import one_hot_encode
from src.data.dataloader import DataLoader

def load_hyperparam_ranges(path="src/optimize/hyperparam_ranges.json"):
    with open(path, "r") as f:
        return json.load(f)

def objective(trial, device, train_data, val_data, ranges):
    # Suggest hyperparameters from loaded ranges
    hidden_dim = trial.suggest_int('hidden_dim', ranges['hidden_dim']['low'], ranges['hidden_dim']['high'])
    latent_dim = trial.suggest_int('latent_dim', ranges['latent_dim']['low'], ranges['latent_dim']['high'])
    lr = trial.suggest_loguniform('lr', ranges['lr']['low'], ranges['lr']['high'])

    model = CVAE(hidden_dim=hidden_dim, latent_dim=latent_dim).to(device)
    optimizer = optim.Adam(model.parameters(), lr=lr)
    model.train()

    # Split train_data into train/val DataLoader
    train_loader = train_data
    val_loader = val_data

    for epoch in range(10):
        model.train()
        for data, labels in train_loader:
            data = data.to(device)
            labels = labels.to(device)
            labels_one_hot = one_hot_encode(labels, num_classes=10)
            optimizer.zero_grad()
            recon_batch, mu, logvar = model(data, labels_one_hot)
            loss, _, _ = loss_function(recon_batch, data, mu, logvar)
            loss.backward()
            optimizer.step()

    # Evaluate on validation set
    model.eval()
    val_loss = 0
    with torch.no_grad():
        for data, labels in val_loader:
            data = data.to(device)
            labels = labels.to(device)
            labels_one_hot = one_hot_encode(labels, num_classes=10)
            recon_batch, mu, logvar = model(data, labels_one_hot)
            loss, _, _ = loss_function(recon_batch, data, mu, logvar)
            val_loss += loss.item()
    avg_val_loss = val_loss / len(val_loader.dataset)
    return avg_val_loss

def optimize(full_dataset, device, n_trials=20, batch_size=64):
    ranges = load_hyperparam_ranges()

    indices = list(range(len(full_dataset)))
    train_idx, val_idx = train_test_split(indices, test_size=0.2, random_state=42)
    train_subset = torch.utils.data.Subset(full_dataset, train_idx)
    val_subset = torch.utils.data.Subset(full_dataset, val_idx)
    train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)

    def wrapped_objective(trial):
        return objective(trial, device, train_loader, val_loader, ranges)

    study = optuna.create_study(direction="minimize")
    study.optimize(wrapped_objective, n_trials=n_trials, show_progress_bar=True)
    
    # Save best hyperparameters
    best_params = study.best_params
    with open("best_hyperparams.json", "w") as f:
        json.dump(best_params, f, indent=4)
    print("Best hyperparameters saved to best_hyperparams.json:", best_params)
    return study


def main():

    parser = argparse.ArgumentParser()
    parser.add_argument("--device", default="cuda" if torch.cuda.is_available() else "cpu", help="Device to use")
    parser.add_argument("--n-trials", type=int, default=20, help="Number of Optuna trials")
    parser.add_argument("--batch-size", type=int, default=64, help="Batch size")
    parser.add_argument("--hyperparam-ranges", type=str, default="src/optimize/hyperparam_ranges.json", help="Path to hyperparameter ranges JSON file")
    args = parser.parse_args()
    
    dataset = 
    
    
if __name__ == "__main__":
    main()
    