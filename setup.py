from setuptools import setup, find_packages

setup(
    name='cvae-demo',
    version='0.1.0',
    packages=find_packages(where='src'),
    package_dir={'': 'src'},
    install_requires=[
        'torch',
        'torchvision',
        'numpy',
        'pandas',
        'scikit-learn',
        'optuna',
        'wandb',
        'fastapi',
        'uvicorn',
        'streamlit',
        'pydantic',
    ],
) 