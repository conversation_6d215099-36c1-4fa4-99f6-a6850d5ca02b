#!/usr/bin/env python3
"""
Interactive setup script for W&B configuration
"""

import os
import sys

def setup_wandb_env():
    """Interactive setup for W&B .env file"""
    
    print("🚀 W&B Setup for CVAE Project")
    print("=" * 40)
    
    # Check if .env already exists
    env_path = ".env"
    if os.path.exists(env_path):
        print(f"⚠️  .env file already exists at {env_path}")
        overwrite = input("Do you want to overwrite it? (y/N): ").lower().strip()
        if overwrite != 'y':
            print("Setup cancelled.")
            return False
    
    print("\n📋 Please provide the following information:")
    print("(Press Enter to use default values)")
    
    # Get API key
    print("\n1. W&B API Key (Required)")
    print("   Get it from: https://wandb.ai/authorize")
    api_key = input("   Enter your W&B API key: ").strip()
    
    if not api_key:
        print("❌ API key is required. Setup cancelled.")
        return False
    
    # Get project name
    print("\n2. Project Name")
    project = input("   Enter project name (default: cvae-demo): ").strip()
    if not project:
        project = "cvae-demo"
    
    # Get entity (optional)
    print("\n3. Entity (Optional)")
    print("   Your W&B username or team name")
    entity = input("   Enter entity (leave empty to skip): ").strip()
    
    # Get mode
    print("\n4. Logging Mode")
    print("   online  - Log to W&B servers (default)")
    print("   offline - Save locally, sync later")
    print("   disabled - Disable W&B")
    mode = input("   Enter mode (default: online): ").strip()
    if not mode:
        mode = "online"
    
    # Create .env content
    env_content = f"""# Weights & Biases Configuration
# Generated by setup_wandb.py

WANDB_API_KEY={api_key}
WANDB_PROJECT={project}
"""
    
    if entity:
        env_content += f"WANDB_ENTITY={entity}\n"
    
    if mode != "online":
        env_content += f"WANDB_MODE={mode}\n"
    
    env_content += """
# Optional: Disable W&B prompts
# WANDB_SILENT=true

# Optional: Custom base URL for W&B Server
# WANDB_BASE_URL=https://your-wandb-server.com
"""
    
    # Write .env file
    try:
        with open(env_path, 'w') as f:
            f.write(env_content)
        
        print(f"\n✅ .env file created successfully at {env_path}")
        print("\n📝 Configuration Summary:")
        print(f"   Project: {project}")
        if entity:
            print(f"   Entity: {entity}")
        print(f"   Mode: {mode}")
        print(f"   API Key: {'*' * (len(api_key) - 4) + api_key[-4:]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def test_setup():
    """Test the W&B setup"""
    print("\n🧪 Testing W&B setup...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from src.utils.wandb_utils import setup_wandb_auth
        
        if setup_wandb_auth():
            print("✅ W&B authentication test passed!")
            return True
        else:
            print("❌ W&B authentication test failed!")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the project root directory")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main setup function"""
    
    # Check if we're in the right directory
    if not os.path.exists("src/utils/wandb_utils.py"):
        print("❌ Please run this script from the project root directory")
        print("   (where src/ folder is located)")
        return
    
    # Setup .env file
    if setup_wandb_env():
        print("\n" + "=" * 40)
        
        # Test the setup
        if test_setup():
            print("\n🎉 W&B setup completed successfully!")
            print("\n📚 Next steps:")
            print("   1. Run a quick test: python scripts/test_wandb.py")
            print("   2. Try the demo: python examples/wandb_demo.py")
            print("   3. Start training: python -m src.train.train --use-wandb")
            print("   4. Check your dashboard: https://wandb.ai")
        else:
            print("\n⚠️  Setup completed but test failed.")
            print("   Please check your API key and try again.")
    else:
        print("\n❌ Setup failed. Please try again.")

if __name__ == "__main__":
    main()
