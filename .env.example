# Weights & Biases Configuration
# Copy this file to .env and fill in your actual API key
# Get your API key from: https://wandb.ai/authorize

WANDB_API_KEY=your_wandb_api_key_here

# Optional: Set default W&B project name
WANDB_PROJECT=cvae-demo

# Optional: Set W&B entity (your username or team name)
# WANDB_ENTITY=your_username_or_team

# Optional: Set W&B mode (online, offline, disabled)
# WANDB_MODE=online

# Optional: Disable W&B prompts
# WANDB_SILENT=true
