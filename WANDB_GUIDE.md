# Weights & Biases Integration Guide

This guide explains how to use Weights & Biases (W&B) for experiment tracking in the CVAE project.

## Features

The W&B integration provides comprehensive experiment tracking including:

- **Loss Logging**: Track training and validation losses (total, BCE, KLD)
- **Image Logging**: Visualize generated samples and reconstructions
- **Latent Space Visualization**: 2D scatter plots for 2D latent spaces
- **Hyperparameter Tracking**: Automatic logging of model configuration
- **Real-time Monitoring**: Live updates during training

## Setup

1. **Install W&B** (already included in requirements.txt):
   ```bash
   pip install wandb
   ```

2. **Login to W&B**:
   ```bash
   wandb login
   ```
   Follow the prompts to authenticate with your W&B account.

## Usage

### Basic Training with W&B

Run training with W&B logging enabled (default):

```bash
python -m src.train.train --epochs 10 --use-wandb
```

### Custom W&B Configuration

```bash
python -m src.train.train \
    --epochs 20 \
    --use-wandb \
    --wandb-project "my-cvae-experiments" \
    --wandb-run-name "experiment-1" \
    --wandb-tags "baseline" "fashion-mnist"
```

### Disable W&B

To train without W&B logging:

```bash
python -m src.train.train --epochs 10 --no-use-wandb
```

## Demo Script

Run the comprehensive demo to see all W&B features:

```bash
python examples/wandb_demo.py
```

This demo:
- Uses a 2D latent space for visualization
- Runs for 5 epochs with comprehensive logging
- Shows all W&B integration features

## W&B Dashboard Features

### 1. Loss Tracking
- **Training Losses**: `train/total_loss`, `train/bce_loss`, `train/kld_loss`
- **Validation Losses**: `test/total_loss`, `test/bce_loss`, `test/kld_loss`
- Real-time loss curves and comparisons across runs

### 2. Generated Images
- **Generated Samples**: Images generated from random latent codes for each class
- **Reconstructions**: Original vs reconstructed image pairs
- Updated periodically during training

### 3. Latent Space Visualization
- 2D scatter plot of latent representations (only for 2D latent spaces)
- Color-coded by Fashion-MNIST class
- Shows how the model learns to organize the latent space

### 4. Hyperparameter Tracking
- Model architecture parameters
- Training configuration
- Automatic comparison across experiments

## Configuration Files

### hyperparam_config.json
```json
{
    "input_dim": 784,
    "hidden_dim": 400,
    "latent_dim": 20,
    "num_classes": 10,
    "lr": 0.001,
    "batch_size": 64,
    "beta": 1.0,
    "optimizer": "Adam"
}
```

### For 2D Latent Space Visualization
```json
{
    "input_dim": 784,
    "hidden_dim": 256,
    "latent_dim": 2,
    "num_classes": 10,
    "lr": 0.001,
    "batch_size": 128
}
```

## Advanced Usage

### Custom Logging in Your Code

```python
from src.utils.wandb_utils import init_wandb, log_losses, log_images

# Initialize W&B
config = {"lr": 0.001, "batch_size": 64}
init_wandb(config, project_name="my-project")

# Log losses
log_losses(epoch=1, train_loss=0.5, train_bce=0.3, train_kld=0.2)

# Log custom images
log_images(image_tensor, labels_tensor, epoch=1, prefix="custom")
```

### Hyperparameter Sweeps

Create a sweep configuration file `sweep.yaml`:

```yaml
program: src/train/train.py
method: bayes
metric:
  name: test/total_loss
  goal: minimize
parameters:
  hidden_dim:
    values: [128, 256, 512]
  latent_dim:
    values: [10, 20, 50]
  lr:
    min: 0.0001
    max: 0.01
```

Run the sweep:
```bash
wandb sweep sweep.yaml
wandb agent <sweep_id>
```

## Tips

1. **Project Organization**: Use consistent project names and tags
2. **Run Names**: Use descriptive run names for easy identification
3. **2D Latent Space**: Set `latent_dim: 2` for latent space visualization
4. **Image Frequency**: Adjust image logging frequency for longer training runs
5. **Resource Usage**: W&B logging adds minimal overhead but consider disabling for very large experiments

## Troubleshooting

- **Login Issues**: Run `wandb login` and check your API key
- **Network Issues**: W&B works offline and syncs when connection is restored
- **Large Images**: Image logging is optimized but consider reducing frequency for very large datasets
- **Memory Usage**: Latent space visualization uses a subset of data to manage memory

## Example Output

After running with W&B, you'll see:
- Real-time loss curves in your browser
- Generated image galleries showing training progress
- Latent space evolution (for 2D latent spaces)
- Hyperparameter comparison tables
- Run comparison tools

Visit your W&B dashboard at https://wandb.ai to explore your experiments!
